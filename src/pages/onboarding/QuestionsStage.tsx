import { useState, useEffect } from 'react'
import robotImage from '@/assets/robot.png'
import TextArea from './TextArea'
import {
  onboardingQuestions,
  mockAIResponses,
  welcomeTexts,
  type QuestionOption,
} from './questionData'

interface QuestionsStageProps {
  currentQuestionIndex: number
  onQuestionComplete: (index: number) => void
  onAllQuestionsComplete: () => void
}

const QuestionsStage = ({
  currentQuestionIndex,
  onQuestionComplete,
  onAllQuestionsComplete,
}: QuestionsStageProps) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentTexts, setCurrentTexts] = useState<string[]>([])
  const [textComplete, setTextComplete] = useState(false)

  const currentQuestion = onboardingQuestions[currentQuestionIndex]

  // 初始化当前题目的文本
  useEffect(() => {
    if (currentQuestionIndex === 0) {
      // 第一题显示welcome文本
      setCurrentTexts(welcomeTexts)
      setTextComplete(false)
    } else {
      // 其他题目等待API返回文本，先不显示任何文本
      setCurrentTexts([])
      setTextComplete(true) // 直接显示题目，等待用户选择
    }
    setIsProcessing(false)
  }, [currentQuestionIndex])

  const handleOptionSelect = (option: QuestionOption) => {
    if (isProcessing) return

    setIsProcessing(true)

    // 模拟API调用
    setTimeout(() => {
      const response = mockAIResponses[option.id]
      if (response) {
        if (currentQuestionIndex < onboardingQuestions.length - 1) {
          // 不是最后一题，显示API响应文本，然后进入下一题
          setCurrentTexts([response.summary])
          setTextComplete(false)

          // 等待文本显示完成后自动进入下一题
          // 这个逻辑在 handleTextAreaComplete 中处理
        } else {
          // 最后一题，进入思考阶段
          onAllQuestionsComplete()
        }
      }
    }, 500)
  }

  const handleSkipClick = () => {
    const skipOption: QuestionOption = {
      id: `${currentQuestion.id}_skip`,
      text: '稍后再说',
    }
    handleOptionSelect(skipOption)
  }

  const handleTextAreaComplete = () => {
    if (currentQuestionIndex === 0) {
      // 第一题，文本显示完成后显示题目
      setTextComplete(true)
    } else {
      // 其他题目，文本显示完成后进入下一题
      setTimeout(() => {
        onQuestionComplete(currentQuestionIndex)
      }, 1000)
    }
  }

  if (!currentQuestion) {
    return null
  }

  return (
    <div className='flex w-full flex-col items-center space-y-6'>
      {/* 机器人头像 */}
      <div className='h-16 w-16'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {/* 文本区域 */}
      {currentTexts.length > 0 && (
        <TextArea texts={currentTexts} onComplete={handleTextAreaComplete} />
      )}

      {/* 问题区域 */}
      {textComplete && (
        <div className='w-full max-w-sm rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
          <h3 className='mb-6 text-center text-lg font-medium text-gray-800'>
            {currentQuestion.text}
          </h3>

          <div className='grid grid-cols-2 gap-3'>
            {currentQuestion.options
              .filter((option) => !option.id.includes('skip'))
              .map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleOptionSelect(option)}
                  disabled={isProcessing}
                  className='rounded-lg border border-gray-200 bg-gray-50 p-3 text-center text-sm text-gray-700 transition-colors hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50'
                >
                  {option.text}
                </button>
              ))}
          </div>

          <div className='mt-4 text-center'>
            <button
              onClick={handleSkipClick}
              disabled={isProcessing}
              className='text-xs text-gray-400 transition-colors hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50'
            >
              稍后再说
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

export default QuestionsStage
