import { useHistory } from 'react-router-dom'

const OnboardingHeader = () => {
  const history = useHistory()

  const handleBackClick = () => {
    history.goBack()
  }

  return (
    <div className='flex items-center justify-between border-b border-black/5 bg-[#F5F5F5] px-4 py-2'>
      <div className='flex items-center justify-start'>
        <button
          onClick={handleBackClick}
          className='h-8 w-8 p-1 cursor-pointer'
        >
          <svg width="23" height="24" viewBox="0 0 23 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path fillRule="evenodd" clipRule="evenodd" d="M8.84317 12L15.9142 19.0711L14.5 20.4853L6.72185 12.7071C6.33132 12.3166 6.33132 11.6834 6.72185 11.2929L14.5 3.51471L15.9142 4.92892L8.84317 12Z" />
          </svg>
        </button>
      </div>
      <div className='flex items-center justify-center'>
        <PERSON>
      </div>
      <div className='flex items-center justify-end w-8'>
        {/* 占位，保持布局平衡 */}
      </div>
    </div>
  )
}

export default OnboardingHeader
