import { useState, useEffect } from 'react'
import { IonPage, IonContent } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import robotImage from '@/assets/robot.png'
import TypewriterText from '@/components/TypewriterText'
import {
  onboardingQuestions,
  mockAIResponses,
  thinkingTexts,
  completionTexts,
  type QuestionOption,
} from './questionData'

// 添加 fadeIn 动画样式
const fadeInStyle = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`

// 引导页面的不同阶段
enum OnboardingStage {
  WELCOME = 'welcome',
  QUESTIONS = 'questions',
  THINKING = 'thinking',
  COMPLETE = 'complete',
}

const OnboardingPage = () => {
  const [currentStage, setCurrentStage] = useState<OnboardingStage>(
    OnboardingStage.WELCOME,
  )
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const history = useHistory()

  const handleEnterChat = () => {
    // 跳转到主聊天页面
    history.push('/conversation/main')
  }

  return (
    <IonPage>
      <style>{fadeInStyle}</style>
      <IonContent className='bg-gray-100'>
        <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
          {/* 页面标题 */}
          <div className='flex items-center justify-center py-4'>
            <h1 className='text-lg font-semibold text-gray-800'>Tina Chat</h1>
          </div>

          {/* 主要内容区域 */}
          <div className='flex flex-1 flex-col items-center justify-center px-6'>
            {currentStage === OnboardingStage.WELCOME && (
              <WelcomeStage
                onComplete={() => setCurrentStage(OnboardingStage.QUESTIONS)}
              />
            )}

            {currentStage === OnboardingStage.QUESTIONS && (
              <QuestionsStage
                currentQuestionIndex={currentQuestionIndex}
                onQuestionComplete={(index) =>
                  setCurrentQuestionIndex(index + 1)
                }
                onAllQuestionsComplete={() =>
                  setCurrentStage(OnboardingStage.THINKING)
                }
              />
            )}

            {currentStage === OnboardingStage.THINKING && (
              <ThinkingStage
                onComplete={() => setCurrentStage(OnboardingStage.COMPLETE)}
              />
            )}

            {currentStage === OnboardingStage.COMPLETE && (
              <CompleteStage onEnterChat={handleEnterChat} />
            )}
          </div>

          {/* 底部文字 */}
          <div className='flex items-center justify-center py-6'>
            <p className='text-sm text-gray-500'>命你所想，知你所念</p>
          </div>
        </div>
      </IonContent>
    </IonPage>
  )
}

// 欢迎阶段组件
const WelcomeStage = ({ onComplete }: { onComplete: () => void }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)

  const welcomeTexts = [
    '已经配置到最懂您的私人助手了！',
    '为了更精准为您推荐本地资讯和服务，我们需要获取您的地理位置',
    'Tina 可以读取您的地理位置吗？',
    '最后，有请您的专属私人助手：',
  ]

  const handleTextComplete = () => {
    if (currentTextIndex < welcomeTexts.length - 1) {
      // 延迟一下再显示下一段文字
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 800)
    } else {
      // 所有文字显示完成，显示按钮
      setTimeout(() => {
        setShowButton(true)
      }, 1000)
    }
  }

  return (
    <div className='flex flex-col items-center space-y-6'>
      {/* 机器人头像 */}
      <div className='h-24 w-24'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {/* 欢迎文字 */}
      <div className='min-h-[200px] space-y-4 text-center'>
        {welcomeTexts.map((text, index) => (
          <div key={index} className='min-h-[24px]'>
            {index <= currentTextIndex && (
              <TypewriterText
                text={text}
                speed={60}
                onComplete={
                  index === currentTextIndex ? handleTextComplete : undefined
                }
                className={
                  index === 0
                    ? 'text-lg text-gray-800'
                    : 'text-base text-gray-600'
                }
              />
            )}
          </div>
        ))}
      </div>

      {/* 进入下一步按钮 */}
      {showButton && (
        <button
          onClick={onComplete}
          className='animate-fade-in mt-8 rounded-full bg-blue-500 px-8 py-3 text-base font-medium text-white transition-all duration-300'
          style={{ animation: 'fadeIn 0.5s ease-in-out forwards' }}
        >
          遇见您独属的 Tina →
        </button>
      )}
    </div>
  )
}

// 问答阶段组件
const QuestionsStage = ({
  currentQuestionIndex,
  onQuestionComplete,
  onAllQuestionsComplete,
}: {
  currentQuestionIndex: number
  onQuestionComplete: (index: number) => void
  onAllQuestionsComplete: () => void
}) => {
  const [showAIResponse, setShowAIResponse] = useState(false)
  const [selectedOption, setSelectedOption] = useState<QuestionOption | null>(
    null,
  )
  const [aiResponseText, setAIResponseText] = useState('')
  const [showNextQuestion, setShowNextQuestion] = useState(false)

  const currentQuestion = onboardingQuestions[currentQuestionIndex]

  // 当问题索引改变时重置状态
  useEffect(() => {
    setShowAIResponse(false)
    setSelectedOption(null)
    setAIResponseText('')
    setShowNextQuestion(false)
  }, [currentQuestionIndex])

  const handleOptionSelect = (option: QuestionOption) => {
    setSelectedOption(option)

    // 模拟AI回复
    const response = mockAIResponses[option.id]
    if (response) {
      setAIResponseText(response.summary)
      setShowAIResponse(true)
    }
  }

  const handleAIResponseComplete = () => {
    // AI回复完成后，延迟显示下一个问题的引导语
    setTimeout(() => {
      if (currentQuestionIndex < onboardingQuestions.length - 1) {
        const response = mockAIResponses[selectedOption?.id || '']
        if (response) {
          setAIResponseText(response.nextIntroduction)
          setShowNextQuestion(true)
        }
      } else {
        // 所有问题完成
        onAllQuestionsComplete()
      }
    }, 1000)
  }

  const handleNextQuestionComplete = () => {
    // 清屏，进入下一题
    setTimeout(() => {
      // 重置当前问题的状态
      setShowAIResponse(false)
      setSelectedOption(null)
      setAIResponseText('')
      setShowNextQuestion(false)
      onQuestionComplete(currentQuestionIndex)
    }, 800)
  }

  if (!currentQuestion) return null

  return (
    <div className='flex w-full flex-col items-center'>
      {/* 机器人头像 */}
      <div className='mb-6 h-16 w-16'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {!showAIResponse && (
        <>
          {/* 问题引导语 */}
          {currentQuestion.introduction && (
            <div className='mb-6 space-y-2 text-center'>
              {currentQuestion.introduction.split('\n').map((line, index) => (
                <p key={index} className='text-base text-gray-600'>
                  {line}
                </p>
              ))}
            </div>
          )}

          {/* 问题和选项 */}
          <div className='w-full max-w-sm rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
            <h3 className='mb-6 text-center text-lg font-medium text-gray-800'>
              {currentQuestion.text}
            </h3>

            <div className='grid grid-cols-2 gap-3'>
              {currentQuestion.options.map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleOptionSelect(option)}
                  className='rounded-lg border border-gray-200 bg-gray-50 p-3 text-center text-sm text-gray-700 transition-colors hover:bg-gray-100'
                >
                  {option.text}
                </button>
              ))}
            </div>

            <div className='mt-4 text-center'>
              <span className='text-xs text-gray-400'>稍后再说</span>
            </div>
          </div>
        </>
      )}

      {/* AI回复阶段 */}
      {showAIResponse && (
        <div className='max-w-sm text-center'>
          <TypewriterText
            text={aiResponseText}
            speed={50}
            onComplete={
              showNextQuestion
                ? handleNextQuestionComplete
                : handleAIResponseComplete
            }
            className='text-base text-gray-700'
          />
        </div>
      )}
    </div>
  )
}

// 思考阶段组件
const ThinkingStage = ({ onComplete }: { onComplete: () => void }) => {
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0)
  const [allThinkingComplete, setAllThinkingComplete] = useState(false)
  const [showCollapsed, setShowCollapsed] = useState(false)

  const handleThinkingComplete = () => {
    if (currentThinkingIndex < thinkingTexts.length - 1) {
      setTimeout(() => {
        setCurrentThinkingIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有思考文字显示完成
      setTimeout(() => {
        setAllThinkingComplete(true)
        // 再延迟一下显示折叠效果
        setTimeout(() => {
          setShowCollapsed(true)
          // 折叠完成后进入下一阶段
          setTimeout(() => {
            onComplete()
          }, 1000)
        }, 1500)
      }, 1000)
    }
  }

  return (
    <div className='flex w-full flex-col items-center'>
      {/* 机器人头像 - 移动到左侧 */}
      <div
        className={`transition-all duration-1000 ${showCollapsed ? 'ml-4 self-start' : 'self-center'}`}
      >
        <div className='mb-6 h-16 w-16'>
          <img
            src={robotImage}
            alt='机器人助手'
            className='h-full w-full object-contain'
          />
        </div>
      </div>

      {/* 思考过程文字 */}
      <div
        className={`max-w-sm transition-all duration-1000 ${showCollapsed ? 'h-0 overflow-hidden opacity-0' : 'opacity-100'}`}
      >
        <div className='space-y-4'>
          {thinkingTexts.map((text, index) => (
            <div key={index} className='min-h-[24px]'>
              {index <= currentThinkingIndex && (
                <TypewriterText
                  text={text}
                  speed={40}
                  onComplete={
                    index === currentThinkingIndex
                      ? handleThinkingComplete
                      : undefined
                  }
                  className='text-base text-gray-700'
                />
              )}
            </div>
          ))}
        </div>
      </div>

      {/* 思考完成提示 */}
      {allThinkingComplete && !showCollapsed && (
        <div className='mt-4 text-center'>
          <p className='text-sm text-gray-500'>思考完成，正在为您准备...</p>
        </div>
      )}
    </div>
  )
}

// 完成阶段组件
const CompleteStage = ({ onEnterChat }: { onEnterChat: () => void }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)

  const handleTextComplete = () => {
    if (currentTextIndex < completionTexts.length - 1) {
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有文字显示完成，显示按钮
      setTimeout(() => {
        setShowButton(true)
      }, 1500)
    }
  }

  return (
    <div className='flex w-full flex-col items-center'>
      {/* 机器人头像 - 保持在左侧位置 */}
      <div className='mb-6 ml-4 self-start'>
        <div className='h-16 w-16'>
          <img
            src={robotImage}
            alt='机器人助手'
            className='h-full w-full object-contain'
          />
        </div>
      </div>

      {/* 完成文字 */}
      <div className='min-h-[150px] max-w-sm space-y-4 text-center'>
        {completionTexts.map((text, index) => (
          <div key={index} className='min-h-[24px]'>
            {index <= currentTextIndex && (
              <TypewriterText
                text={text}
                speed={50}
                onComplete={
                  index === currentTextIndex ? handleTextComplete : undefined
                }
                className='text-base text-gray-700'
              />
            )}
          </div>
        ))}
      </div>

      {/* 进入聊天按钮 */}
      {showButton && (
        <button
          onClick={onEnterChat}
          className='animate-fade-in mt-8 rounded-full bg-wechatBrand-3 px-8 py-3 text-base font-medium text-white transition-all duration-300 hover:bg-wechatBrand-2'
          style={{ animation: 'fadeIn 0.5s ease-in-out forwards' }}
        >
          开始聊天 →
        </button>
      )}
    </div>
  )
}

export default OnboardingPage
