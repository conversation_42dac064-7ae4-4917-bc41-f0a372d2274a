import { useState, useEffect } from 'react'
import { IonPage, IonContent } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import robotImage from '@/assets/robot.png'
import TypewriterText from '@/components/TypewriterText'
import ChatBubble from './ChatBubble'
import OnboardingFooter from './OnboardingFooter'
import OnboardingHeader from './OnboardingHeader'
import ThinkingBubble from './ThinkingBubble'
import {
  onboardingQuestions,
  mockAIResponses,
  thinkingTexts,
  completionTexts,
  type QuestionOption,
} from './questionData'

// 添加 fadeIn 动画样式
const fadeInStyle = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`

// 引导页面的不同阶段
enum OnboardingStage {
  WELCOME = 'welcome',
  QUESTIONS = 'questions',
  THINKING = 'thinking',
  COMPLETE = 'complete',
}

const OnboardingPage = () => {
  const [currentStage, setCurrentStage] = useState<OnboardingStage>(
    OnboardingStage.WELCOME,
  )
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const history = useHistory()

  const handleEnterChat = () => {
    // 跳转到主聊天页面
    history.push('/conversation/1')
  }

  return (
    <IonPage>
      <style>{fadeInStyle}</style>
      <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
        {/* 顶部 AppBar */}
        <OnboardingHeader />

        {/* 主要内容区域 */}
        <IonContent className='bg-gray-100'>
          <div className='flex flex-col space-y-4 px-4 py-4'>
            {currentStage === OnboardingStage.WELCOME && (
              <WelcomeStage
                onComplete={() => setCurrentStage(OnboardingStage.QUESTIONS)}
              />
            )}

            {currentStage === OnboardingStage.QUESTIONS && (
              <QuestionsStage
                currentQuestionIndex={currentQuestionIndex}
                onQuestionComplete={(index) =>
                  setCurrentQuestionIndex(index + 1)
                }
                onAllQuestionsComplete={() =>
                  setCurrentStage(OnboardingStage.THINKING)
                }
              />
            )}

            {currentStage === OnboardingStage.THINKING && (
              <ThinkingStage
                onComplete={() => setCurrentStage(OnboardingStage.COMPLETE)}
              />
            )}

            {currentStage === OnboardingStage.COMPLETE && (
              <CompleteStage onEnterChat={handleEnterChat} />
            )}
          </div>
        </IonContent>

        {/* 底部 Footer */}
        <OnboardingFooter />
      </div>
    </IonPage>
  )
}

// 欢迎阶段组件
const WelcomeStage = ({ onComplete }: { onComplete: () => void }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)

  const welcomeTexts = [
    '♪ 滴—答♪ 初次见面！',
    '欢迎来到「私人助理」俱乐部',
    '仅需 1分钟 完成 4道选择题',
    '系统就能自动锁定最适合您的助理类型！',
  ]

  const handleTextComplete = () => {
    if (currentTextIndex < welcomeTexts.length - 1) {
      // 延迟一下再显示下一段文字
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 800)
    } else {
      // 所有文字显示完成，自动进入问答阶段
      setTimeout(() => {
        onComplete()
      }, 1500)
    }
  }

  return (
    <div className='flex flex-col items-center space-y-6'>
      {/* 机器人头像 */}
      <div className='h-24 w-24'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {/* 欢迎文字 */}
      <div className='min-h-[200px] space-y-4 text-center'>
        {welcomeTexts.map((text, index) => (
          <div key={index} className='min-h-[24px]'>
            {index <= currentTextIndex && (
              <TypewriterText
                text={text}
                speed={60}
                onComplete={
                  index === currentTextIndex ? handleTextComplete : undefined
                }
                className={
                  index === 0
                    ? 'text-lg text-gray-800'
                    : 'text-base text-gray-600'
                }
              />
            )}
          </div>
        ))}
      </div>
    </div>
  )
}

// 问答阶段组件
const QuestionsStage = ({
  currentQuestionIndex,
  onQuestionComplete,
  onAllQuestionsComplete,
}: {
  currentQuestionIndex: number
  onQuestionComplete: (index: number) => void
  onAllQuestionsComplete: () => void
}) => {
  const [isProcessing, setIsProcessing] = useState(false)
  const [currentText, setCurrentText] = useState('')
  const [showCurrentText, setShowCurrentText] = useState(false)
  const [textComplete, setTextComplete] = useState(false)

  const currentQuestion = onboardingQuestions[currentQuestionIndex]

  // 初始化文本内容
  useEffect(() => {
    if (currentQuestionIndex === 0) {
      // 第一题显示welcome文本
      const welcomeTexts = [
        '已经配置到最懂您的私人助手了！',
        '为了更精准为您推荐本地资讯和服务，我们需要获取您的地理位置',
        'Tina 可以读取您的地理位置吗？',
        '最后，有请您的专属私人助手：',
      ]
      setCurrentText(welcomeTexts.join('\n\n'))
      setShowCurrentText(true)
      setTextComplete(false)
    } else {
      // 其他题目等待API返回文本
      setShowCurrentText(false)
      setTextComplete(false)
    }
    setIsProcessing(false)
  }, [currentQuestionIndex])

  const handleOptionSelect = (option: QuestionOption) => {
    if (isProcessing) return // 防止重复点击

    setIsProcessing(true)

    // 模拟API调用
    setTimeout(() => {
      const response = mockAIResponses[option.id]
      if (response) {
        if (currentQuestionIndex < onboardingQuestions.length - 1) {
          // 不是最后一题，设置API返回文本并进入下一题
          setCurrentText(response.summary)
          setShowCurrentText(true)
          setTextComplete(false)

          // 文本显示完成后进入下一题
          setTimeout(() => {
            onQuestionComplete(currentQuestionIndex)
          }, 2000) // 给文本显示时间
        } else {
          // 最后一题，进入思考阶段
          onAllQuestionsComplete()
        }
      }
    }, 500) // 模拟API延迟
  }

  const handleSkipClick = () => {
    if (isProcessing) return

    setIsProcessing(true)

    // 使用跳过的回复
    const skipResponse = mockAIResponses[`${currentQuestion.id}_skip`] || {
      summary: '没关系，我们可以稍后再聊这个话题～',
      nextIntroduction: '让我们继续下一个问题～',
    }

    setTimeout(() => {
      if (currentQuestionIndex < onboardingQuestions.length - 1) {
        setCurrentText(skipResponse.summary)
        setShowCurrentText(true)
        setTextComplete(false)

        setTimeout(() => {
          onQuestionComplete(currentQuestionIndex)
        }, 2000)
      } else {
        onAllQuestionsComplete()
      }
    }, 500)
  }

  const handleTextComplete = () => {
    setTextComplete(true)
  }

  if (!currentQuestion) return null

  return (
    <div className='flex w-full flex-col items-center space-y-6'>
      {/* 机器人头像 */}
      <div className='h-16 w-16'>
        <img
          src={robotImage}
          alt='机器人助手'
          className='h-full w-full object-contain'
        />
      </div>

      {/* 文本区域 */}
      {showCurrentText && (
        <div className='space-y-4 text-center'>
          <div className='min-h-[24px]'>
            <TypewriterText
              text={currentText}
              speed={50}
              onComplete={handleTextComplete}
              className='text-base text-gray-600'
            />
          </div>
        </div>
      )}

      {/* 问题区域 */}
      {textComplete && (
        <div className='w-full max-w-sm rounded-lg border border-gray-200 bg-white p-6 shadow-sm'>
          <h3 className='mb-6 text-center text-lg font-medium text-gray-800'>
            {currentQuestion.text}
          </h3>

          <div className='grid grid-cols-2 gap-3'>
            {currentQuestion.options
              .filter((option) => !option.id.includes('skip'))
              .map((option) => (
                <button
                  key={option.id}
                  onClick={() => handleOptionSelect(option)}
                  disabled={isProcessing}
                  className='rounded-lg border border-gray-200 bg-gray-50 p-3 text-center text-sm text-gray-700 transition-colors hover:bg-gray-100 disabled:cursor-not-allowed disabled:opacity-50'
                >
                  {option.text}
                </button>
              ))}
          </div>

          <div className='mt-4 text-center'>
            <button
              onClick={handleSkipClick}
              disabled={isProcessing}
              className='text-xs text-gray-400 transition-colors hover:text-gray-600 disabled:cursor-not-allowed disabled:opacity-50'
            >
              稍后再说
            </button>
          </div>
        </div>
      )}
    </div>
  )
}

// 思考阶段组件
const ThinkingStage = ({ onComplete }: { onComplete: () => void }) => {
  const [currentThinkingIndex, setCurrentThinkingIndex] = useState(0)
  const [allThinkingComplete, setAllThinkingComplete] = useState(false)
  const [showCollapsed, setShowCollapsed] = useState(false)

  const handleThinkingComplete = () => {
    if (currentThinkingIndex < thinkingTexts.length - 1) {
      setTimeout(() => {
        setCurrentThinkingIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有思考文字显示完成
      setTimeout(() => {
        setAllThinkingComplete(true)
        // 再延迟一下显示折叠效果
        setTimeout(() => {
          setShowCollapsed(true)
          // 折叠完成后进入下一阶段
          setTimeout(() => {
            onComplete()
          }, 1000)
        }, 1500)
      }, 1000)
    }
  }

  return (
    <div className='flex w-full flex-col space-y-4'>
      {/* 思考气泡 - 在同一个气泡内显示多条消息 */}
      <ThinkingBubble isCollapsed={showCollapsed}>
        <div className='space-y-3'>
          {thinkingTexts.map((text, index) => (
            <div key={index}>
              {index <= currentThinkingIndex && (
                <TypewriterText
                  text={text}
                  speed={40}
                  onComplete={
                    index === currentThinkingIndex
                      ? handleThinkingComplete
                      : undefined
                  }
                  className='text-base text-gray-700'
                />
              )}
            </div>
          ))}

          {/* 思考完成提示 */}
          {allThinkingComplete && !showCollapsed && (
            <div className='mt-3 text-sm text-gray-500'>
              思考完成，正在为您准备...
            </div>
          )}
        </div>
      </ThinkingBubble>
    </div>
  )
}

// 完成阶段组件
const CompleteStage = ({ onEnterChat }: { onEnterChat: () => void }) => {
  const [currentTextIndex, setCurrentTextIndex] = useState(0)
  const [showButton, setShowButton] = useState(false)

  const handleTextComplete = () => {
    if (currentTextIndex < completionTexts.length - 1) {
      setTimeout(() => {
        setCurrentTextIndex((prev) => prev + 1)
      }, 1000)
    } else {
      // 所有文字显示完成，显示按钮
      setTimeout(() => {
        setShowButton(true)
      }, 1500)
    }
  }

  return (
    <div className='flex w-full flex-col space-y-4'>
      {/* 完成文字 */}
      <div className='min-h-[150px] space-y-4'>
        {completionTexts.map((text, index) => (
          <div key={index}>
            {index <= currentTextIndex && (
              <ChatBubble showAvatar={index === 0}>
                <TypewriterText
                  text={text}
                  speed={50}
                  onComplete={
                    index === currentTextIndex ? handleTextComplete : undefined
                  }
                  className='text-base text-gray-700'
                />
              </ChatBubble>
            )}
          </div>
        ))}
      </div>

      {/* 进入聊天按钮 */}
      {showButton && (
        <div className='flex justify-center'>
          <button
            onClick={onEnterChat}
            className='animate-fade-in mt-8 rounded-full px-8 py-3 text-base font-medium text-gray-800 transition-all duration-300 hover:opacity-80'
            style={{
              animation: 'fadeIn 0.5s ease-in-out forwards',
              backgroundColor: '#F6F4EE',
            }}
          >
            遇见您独属的 Tina
          </button>
        </div>
      )}
    </div>
  )
}

export default OnboardingPage
