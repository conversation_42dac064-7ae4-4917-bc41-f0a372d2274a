import { useState } from 'react'
import { IonPage, IonContent } from '@ionic/react'
import { useHistory } from 'react-router-dom'
import OnboardingFooter from './OnboardingFooter'
import OnboardingHeader from './OnboardingHeader'
import QuestionsStage from './QuestionsStage'
import ThinkingCompleteStage from './ThinkingCompleteStage'

// 添加 fadeIn 动画样式
const fadeInStyle = `
  @keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
  }
`

// 引导页面的不同阶段
enum OnboardingStage {
  QUESTIONS = 'questions',
  THINKING_COMPLETE = 'thinking_complete',
}

const OnboardingPage = () => {
  const [currentStage, setCurrentStage] = useState<OnboardingStage>(
    OnboardingStage.QUESTIONS,
  )
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0)
  const history = useHistory()

  const handleEnterChat = () => {
    // 跳转到主聊天页面
    history.push('/conversation/1')
  }

  return (
    <IonPage>
      <style>{fadeInStyle}</style>
      <div className='mx-auto flex h-full w-full flex-col overflow-hidden bg-gray-100 sm:max-w-md'>
        {/* 顶部 AppBar */}
        <OnboardingHeader />

        {/* 主要内容区域 */}
        <IonContent className='bg-gray-100'>
          <div className='flex flex-col space-y-4 px-4 py-4'>
            {currentStage === OnboardingStage.QUESTIONS && (
              <QuestionsStage
                currentQuestionIndex={currentQuestionIndex}
                onQuestionComplete={(index) =>
                  setCurrentQuestionIndex(index + 1)
                }
                onAllQuestionsComplete={() =>
                  setCurrentStage(OnboardingStage.THINKING_COMPLETE)
                }
              />
            )}

            {currentStage === OnboardingStage.THINKING_COMPLETE && (
              <ThinkingCompleteStage onEnterChat={handleEnterChat} />
            )}
          </div>
        </IonContent>

        {/* 底部 Footer */}
        <OnboardingFooter />
      </div>
    </IonPage>
  )
}

export default OnboardingPage
